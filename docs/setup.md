# Hướng dẫn Cài đặt và Cấu hình

## Y<PERSON><PERSON> cầu hệ thống
- PHP >= 8.2
- Composer
- Node.js >= 18
- Redis
- MySQL/PostgreSQL

## C<PERSON>c bước cài đặt

### 1. <PERSON><PERSON> dự án
```bash
git clone [repository_url]
cd mmohub-gsm-bot
```

### 2. Cài đặt dependencies
```bash
composer install
npm install
```

### 3. C<PERSON>u hình môi trường
```bash
cp .env.example .env
php artisan key:generate
```

C<PERSON>u hình các biến môi trường trong file `.env`:
```env
APP_NAME="MMOHUB GSM BOT"
APP_ENV=local
APP_DEBUG=true

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mmohub_gsm_bot
DB_USERNAME=root
DB_PASSWORD=

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

QUEUE_CONNECTION=redis
BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis

REVERB_APP_ID=
REVERB_APP_KEY=
REVERB_APP_SECRET=

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0
```

### 4. Cài đặt database
```bash
php artisan migrate
php artisan db:seed
```

### 5. Build assets
```bash
npm run build
```

### 6. Khởi chạy các services

#### Development
```bash
# Terminal 1 - Laravel Server
php artisan serve

# Terminal 2 - Vite Dev Server
npm run dev

# Terminal 3 - Queue Worker
php artisan horizon

# Terminal 4 - WebSocket Server
php artisan reverb:start
```

#### Production
```bash
# Build assets
npm run build

# Start Queue Worker
php artisan horizon

# Start WebSocket Server
php artisan reverb:start
```

## Cấu hình Supervisor
Tạo file cấu hình cho Horizon và Reverb:

### Horizon
```ini
[program:horizon]
process_name=%(program_name)s
command=php /path/to/mmohub-gsm-bot/artisan horizon
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/path/to/mmohub-gsm-bot/storage/logs/horizon.log
stopwaitsecs=3600
```

### Reverb
```ini
[program:reverb]
process_name=%(program_name)s
command=php /path/to/mmohub-gsm-bot/artisan reverb:start
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/path/to/mmohub-gsm-bot/storage/logs/reverb.log
```

## Cấu hình Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/mmohub-gsm-bot/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Kiểm tra cài đặt
1. Truy cập trang admin: `http://localhost:8000/admin`
2. Kiểm tra Horizon Dashboard: `http://localhost:8000/horizon`
3. Kiểm tra Log Viewer: `http://localhost:8000/log-viewer`

## Xử lý sự cố
1. Nếu gặp lỗi permissions:
```bash
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

2. Clear cache:
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

3. Kiểm tra logs:
```bash
tail -f storage/logs/laravel.log
``` 