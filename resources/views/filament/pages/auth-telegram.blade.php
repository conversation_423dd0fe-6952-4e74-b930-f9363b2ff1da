<x-filament-panels::page>
    <!-- <PERSON><PERSON> thông báo về việc chặn Telegram tại Việt Nam -->
    <div class="mb-6">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">
                        Thông báo quan trọng
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>
                            Hiện tại các nhà mạng Việt Nam đang chặn Telegram. Để truy cập được bot, vui lòng bật VPN, proxy hoặc sử dụng DNS khác (như *******, *******).
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div>
        <x-filament-actions::modals/>
    </div>
    <div class="flex items-center justify-center ">
        <div class="w-full max -w-md">
            <x-filament::card class="shadow-lg text-center">
                <div class="text-center">
                    @if(request()->has('error'))
                        <div class="text-warning-50 bg-warning-900 p-4 rounded-lg">
                            <h1 class="text-xl font-semibold">Không thể xác thực tài khoản Telegram</h1>
                            <p class="text-sm">Vui lòng thử lại hoặc liên hệ với quản trị viên</p>
                        </div>
                    @else
                        <x-filament::loading-indicator class="h-20 w-20 inline-block"/>
                        <h1 class="text-xl font-semibold text-center" id="message-auth">Đang xác thực tài khoản ... </h1>
                    @endif
                </div>
            </x-filament::card>
        </div>
    </div>

    @push('scripts')
        <script src="https://telegram.org/js/telegram-web-app.js?56"></script>
        <script>
            function post_to_url(path, params, method) {
                const form = document.createElement("form");
                form.setAttribute("method", method);
                form.setAttribute("action", path);

                for (const key in params) {
                    if (params.hasOwnProperty(key)) {
                        const hiddenField = document.createElement("input");
                        hiddenField.setAttribute("type", "hidden");
                        hiddenField.setAttribute("name", key);
                        hiddenField.setAttribute("value", params[key]);
                        form.appendChild(hiddenField);
                    }
                }

                document.body.appendChild(form);
                form.submit();
            }
            @if(!request()->has('error'))

                let data_telegram = window.Telegram.WebApp.initData;
                // encode data_telegram
                let message_auth = document.getElementById('message-auth');
                if (data_telegram) {
                    post_to_url('/telegram/auth', {
                        data: data_telegram,
                        '_token': '{{ csrf_token() }}'
                    }, 'post');
                } else {
                    new FilamentNotification()
                        .title('Không thể xác thực tài khoản Telegram vui lòng thử lại hoặc liên hệ với quản trị viên 3')
                        .warning()
                        .duration(10000)
                        .send();
                    message_auth.innerHTML = 'Không thể xác thực tài khoản Telegram vui lòng thử lại hoặc liên hệ với quản trị viên';
                }
            @endif
        </script>
    @endpush
</x-filament-panels::page>
